#!/usr/bin/env python3
"""
Test simple du module Confluence intégré dans kbot-load-scheduler.

Ce script teste l'intégration sans dépendances externes (GCP, vraie instance Confluence).
"""

import os
import sys
import json
from datetime import datetime, timezone

# Ajouter le répertoire src au path
sys.path.insert(0, 'src')

def test_imports():
    """Test que tous les imports fonctionnent"""
    print("📦 Test des imports...")
    
    try:
        from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader, LoaderException
        print("✅ ConfluenceLoader importé")
        
        from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
        print("✅ Beans importés")
        
        from kbotloadscheduler.loader.confluence.config import ConfluenceConfig, SearchCriteria
        print("✅ Configuration Confluence importée")
        
        return True
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False


def test_confluence_loader_creation():
    """Test de création du ConfluenceLoader"""
    print("\n🏗️  Test de création du ConfluenceLoader...")
    
    try:
        from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
        
        # Mock simple pour ConfigWithSecret
        class MockConfig:
            def get_confluence_credentials(self, perimeter_code):
                return {"pat_token": "test_token"}
        
        # Définir l'URL requise
        os.environ["CONFLUENCE_URL"] = "https://test.atlassian.net"
        
        # Créer le loader
        loader = ConfluenceLoader(MockConfig())
        print("✅ ConfluenceLoader créé avec succès")
        
        # Vérifier les propriétés
        assert loader._loader_type == "confluence"
        assert loader.confluence_url == "https://test.atlassian.net"
        print("✅ Propriétés du loader vérifiées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        return False


def test_source_bean_creation():
    """Test de création d'une SourceBean pour Confluence"""
    print("\n📋 Test de création d'une SourceBean...")
    
    try:
        from kbotloadscheduler.bean.beans import SourceBean
        
        config = {
            "spaces": ["TEST"],
            "max_results": 100,
            "include_attachments": True,
            "content_types": ["page", "blogpost"]
        }
        
        source = SourceBean(
            id=1,
            code="test_confluence",
            label="Test Confluence Source",
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=int(datetime.now(timezone.utc).timestamp()),
            load_interval=24,
            domain_code="test_domain",
            perimeter_code="test_perimeter",
            force_embedding=False
        )
        
        print("✅ SourceBean créée avec succès")
        print(f"   Type: {source.src_type}")
        print(f"   Configuration: {source.configuration}")
        
        # Vérifier le parsing de la configuration
        parsed_config = source.parse_configuration()
        assert parsed_config["spaces"] == ["TEST"]
        assert parsed_config["max_results"] == 100
        print("✅ Configuration parsée correctement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de la source: {e}")
        return False


def test_configuration_creation():
    """Test de création des configurations Confluence"""
    print("\n⚙️  Test de création des configurations...")
    
    try:
        from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
        from kbotloadscheduler.bean.beans import SourceBean
        
        # Mock et setup
        class MockConfig:
            def get_confluence_credentials(self, perimeter_code):
                return {"pat_token": "test_token"}
        
        os.environ["CONFLUENCE_URL"] = "https://test.atlassian.net"
        os.environ["DEFAULT_SPACE_KEY"] = "TEST"
        
        loader = ConfluenceLoader(MockConfig())
        
        # Créer une source de test
        source = SourceBean(
            id=1,
            code="test",
            label="Test",
            src_type="confluence",
            configuration='{"spaces": ["TEST"], "max_results": 50}',
            last_load_time=0,
            load_interval=24,
            domain_code="test",
            perimeter_code="test",
            force_embedding=False
        )
        
        # Test création de la configuration Confluence
        confluence_config = loader._create_confluence_config(source)
        print("✅ Configuration Confluence créée")
        print(f"   URL: {confluence_config.url}")
        print(f"   Espace par défaut: {confluence_config.default_space_key}")
        
        # Test création des critères de recherche
        search_criteria = loader._create_search_criteria(source)
        print("✅ Critères de recherche créés")
        print(f"   Espaces: {search_criteria.spaces}")
        print(f"   Max résultats: {search_criteria.max_results}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des configurations: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_utility_methods():
    """Test des méthodes utilitaires"""
    print("\n🔧 Test des méthodes utilitaires...")
    
    try:
        from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
        
        class MockConfig:
            def get_confluence_credentials(self, perimeter_code):
                return {"pat_token": "test_token"}
        
        os.environ["CONFLUENCE_URL"] = "https://test.atlassian.net"
        
        loader = ConfluenceLoader(MockConfig())
        
        # Test extraction bucket
        bucket = loader._extract_bucket_from_path("gs://test-bucket/path/to/file")
        assert bucket == "test-bucket"
        print("✅ Extraction bucket GCS")
        
        # Test extraction prefix
        prefix = loader._extract_prefix_from_path("gs://test-bucket/path/to/file")
        assert prefix == "path/to/file"
        print("✅ Extraction prefix GCS")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans les méthodes utilitaires: {e}")
        return False


def main():
    """Fonction principale"""
    print("🧪 Test simple du module Confluence intégré")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Création ConfluenceLoader", test_confluence_loader_creation),
        ("Création SourceBean", test_source_bean_creation),
        ("Création configurations", test_configuration_creation),
        ("Méthodes utilitaires", test_utility_methods),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSÉ" if success else "❌ ÉCHOUÉ"
        print(f"{test_name:.<30} {status}")
        if success:
            passed += 1
    
    print(f"\nRésultat: {passed}/{len(tests)} tests passés")
    
    if passed == len(tests):
        print("\n🎉 TOUS LES TESTS SONT PASSÉS !")
        print("\nLe module Confluence est correctement intégré dans kbot-load-scheduler.")
        print("\nPour tester avec une vraie instance Confluence:")
        print("1. Configurez CONFLUENCE_URL avec votre instance")
        print("2. Configurez les credentials Confluence")
        print("3. Utilisez le script test_confluence_integration.py")
        return 0
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Vérifiez les erreurs ci-dessus.")
        return 1


if __name__ == "__main__":
    exit(main())
